import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/data-service';
import { ApiResponse, CoinInfo } from '@/types/trading';

const dataService = DataService.getInstance();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol');
    const popular = searchParams.get('popular') === 'true';

    if (symbol) {
      // 获取特定币种信息
      const coinInfo = await dataService.getCoinInfo(symbol);
      
      return NextResponse.json<ApiResponse<CoinInfo[]>>({
        success: true,
        data: coinInfo
      });
    }

    if (popular) {
      // 获取热门币种
      const popularCoins = await dataService.getPopularCoins();
      
      return NextResponse.json<ApiResponse<CoinInfo[]>>({
        success: true,
        data: popularCoins
      });
    }

    // 获取所有币种信息（限制数量避免响应过大）
    const allCoins = await dataService.getCoinInfo();
    const limitedCoins = allCoins
      .filter(coin => coin.symbol.endsWith('USDT'))
      .sort((a, b) => b.volume24h - a.volume24h)
      .slice(0, 50); // 只返回前50个

    return NextResponse.json<ApiResponse<CoinInfo[]>>({
      success: true,
      data: limitedCoins
    });

  } catch (error) {
    console.error('获取币种信息失败:', error);
    
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: error instanceof Error ? error.message : '获取币种信息失败'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { symbols } = body;

    if (!symbols || !Array.isArray(symbols)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: '请求参数格式错误，需要symbols数组'
      }, { status: 400 });
    }

    // 批量获取币种信息
    const results: CoinInfo[] = [];
    
    for (const symbol of symbols) {
      try {
        const coinInfo = await dataService.getCoinInfo(symbol);
        if (coinInfo.length > 0) {
          results.push(coinInfo[0]);
        }
      } catch (error) {
        console.error(`获取${symbol}信息失败:`, error);
        // 继续处理其他币种
      }
    }

    return NextResponse.json<ApiResponse<CoinInfo[]>>({
      success: true,
      data: results
    });

  } catch (error) {
    console.error('批量获取币种信息失败:', error);
    
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: error instanceof Error ? error.message : '批量获取币种信息失败'
    }, { status: 500 });
  }
}
