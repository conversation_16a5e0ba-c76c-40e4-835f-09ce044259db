'use client';

import React, { useState, useEffect } from 'react';
import { Search, TrendingUp, TrendingDown } from 'lucide-react';
import { CoinInfo } from '@/types/trading';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface CoinSelectorProps {
  selectedCoin?: string;
  onCoinSelect: (symbol: string) => void;
  className?: string;
}

export function CoinSelector({ selectedCoin, onCoinSelect, className }: CoinSelectorProps) {
  const [coins, setCoins] = useState<CoinInfo[]>([]);
  const [filteredCoins, setFilteredCoins] = useState<CoinInfo[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [showAll, setShowAll] = useState(false);

  // 热门币种列表
  const popularSymbols = [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
    'XRPUSDT', 'DOGEUSDT', 'AVAXUSDT', 'DOTUSDT', 'MATICUSDT'
  ];

  useEffect(() => {
    fetchCoins();
  }, []);

  useEffect(() => {
    filterCoins();
  }, [searchTerm, coins, showAll]);

  const fetchCoins = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/coins?popular=true');
      const result = await response.json();
      
      if (result.success) {
        setCoins(result.data);
      } else {
        console.error('获取币种失败:', result.error);
      }
    } catch (error) {
      console.error('获取币种失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterCoins = () => {
    let filtered = coins;

    if (searchTerm) {
      filtered = coins.filter(coin =>
        coin.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        coin.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (!showAll) {
      filtered = filtered.slice(0, 12);
    }

    setFilteredCoins(filtered);
  };

  const formatPrice = (price: number) => {
    if (price >= 1) {
      return price.toFixed(2);
    } else if (price >= 0.01) {
      return price.toFixed(4);
    } else {
      return price.toFixed(6);
    }
  };

  const formatChange = (change: number) => {
    return `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-4 w-4" />
            <span className="text-sm font-medium">选择交易币种</span>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded-lg animate-pulse" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4" />
            <span className="text-sm font-medium">选择交易币种</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAll(!showAll)}
          >
            {showAll ? '收起' : '查看更多'}
          </Button>
        </div>

        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="搜索币种..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
          {filteredCoins.map((coin) => (
            <Button
              key={coin.symbol}
              variant={selectedCoin === coin.symbol ? "default" : "outline"}
              className="h-auto p-3 flex flex-col items-start space-y-1"
              onClick={() => onCoinSelect(coin.symbol)}
            >
              <div className="flex items-center justify-between w-full">
                <span className="font-semibold text-sm">{coin.name}</span>
                {popularSymbols.includes(coin.symbol) && (
                  <Badge variant="secondary" className="text-xs">热门</Badge>
                )}
              </div>
              
              <div className="flex items-center justify-between w-full">
                <span className="text-xs text-gray-500">{coin.symbol}</span>
                <div className="flex items-center space-x-1">
                  {coin.change24h >= 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500" />
                  )}
                  <span className={`text-xs ${
                    coin.change24h >= 0 ? 'text-green-500' : 'text-red-500'
                  }`}>
                    {formatChange(coin.change24h)}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between w-full">
                <span className="text-sm font-medium">${formatPrice(coin.price)}</span>
                <span className="text-xs text-gray-400">
                  Vol: {(coin.volume24h / 1000000).toFixed(1)}M
                </span>
              </div>
            </Button>
          ))}
        </div>

        {filteredCoins.length === 0 && searchTerm && (
          <div className="text-center py-8 text-gray-500">
            <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>未找到匹配的币种</p>
            <p className="text-sm">请尝试其他搜索词</p>
          </div>
        )}

        {!searchTerm && (
          <div className="mt-4 pt-4 border-t">
            <p className="text-xs text-gray-500 text-center">
              显示热门交易对，支持搜索查找更多币种
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
